package my.com.cmg.rms.service.impl;

import my.com.cmg.rms.dto.FacilityDetailDTO;
import my.com.cmg.rms.dto.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.ItemsMasterDetailDTO;
import my.com.cmg.rms.repository.jooq.RequestDetailRepositoryJooq;
import my.com.cmg.rms.service.IRequestDetailService;
import org.springframework.stereotype.Service;

@Service
public class RequestDetailService implements IRequestDetailService {
  private final RequestDetailRepositoryJooq requestDetailRepositoryJooq;

  public RequestDetailService(final RequestDetailRepositoryJooq requestDetailRepositoryJooq) {
    this.requestDetailRepositoryJooq = requestDetailRepositoryJooq;
  }

  @Override
  public ItemsMasterDetailDTO getItemDetail(Long itemReqSeqno) {
    ItemsMasterDetailDTO response = requestDetailRepositoryJooq.getItemDetail(itemReqSeqno);
    return response;
  }

  @Override
  public ItemPackagingDetailDTO getItemPackagingDetail(Long itemPackagingReqSeqno) {
    ItemPackagingDetailDTO response =
        requestDetailRepositoryJooq.getItemPackagingDetail(itemPackagingReqSeqno);
    return response;
  }

  @Override
  public FacilityDetailDTO getFacilityDetail(Long facilityReqSeqno) {
    FacilityDetailDTO response = requestDetailRepositoryJooq.getFacilityDetail(facilityReqSeqno);
    return response;
  }
}
