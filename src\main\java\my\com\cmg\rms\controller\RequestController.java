package my.com.cmg.rms.controller;

import java.time.LocalDate;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.model.RequestHdr;
import my.com.cmg.rms.service.IRequestService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/api/v1/rms/request")
public class RequestController {
  private final IRequestService requestService;

  public RequestController(final IRequestService requestService) {
    this.requestService = requestService;
  }

  /**
   * Retrieve list of Near Expiration Item
   *
   * @param requestNo - request no
   * @param facilitySeqno - facility seqno
   * @param requestType - request type
   * @param title - title
   * @param category - category
   * @param requestDate - request date
   * @param status - status
   * @param sort - sort by column
   * @param sortDirection - sort direction
   * @param page - page number
   * @param size - page size
   * @return list of RequestListDTO
   */
  @GetMapping("/list")
  public List<RequestListDTO> getRequestList(
      @RequestParam(required = false) String requestNo,
      @RequestParam(required = false) Long facilitySeqno,
      @RequestParam(required = false) String requestType,
      @RequestParam(required = false) String title,
      @RequestParam(required = false) String assignedTo,
      @RequestParam(required = false) String category,
      @RequestParam(required = false) LocalDate requestDateFrom,
      @RequestParam(required = false) LocalDate requestDateTo,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) String sortDirection,
      @RequestParam(required = false) Long page,
      @RequestParam(required = false) Long size) {
    PaginationRequestDTO pgDTO = new PaginationRequestDTO(sort, sortDirection, page, size);
    RequestListSearchDTO requestDTO =
        new RequestListSearchDTO(
            requestNo,
            facilitySeqno,
            requestType,
            title,
            assignedTo,
            category,
            requestDateFrom,
            requestDateTo,
            status);
    List<RequestListDTO> response = requestService.getRequestList(requestDTO, pgDTO);
    return response;
  }

  /**
   * Retrieves the pagination response of near expiration item list. The list can be filtered by
   * near expiration item no, created date from and to, status and item group dropdown.
   *
   * @param requestNo the near expiration item no to filter by
   * @param requestType the request type to filter by
   * @param title the title to filter by
   * @param category the category to filter by
   * @param requestDate the request date to filter by
   * @param status the status to filter by
   * @param page the page number to retrieve
   * @param sort the column to sort by
   * @param sortDirection the sort direction to retrieve
   * @param size the page size to retrieve
   * @return a pagination response of near expiration item list DTOs
   */
  @GetMapping("/list/page")
  public PaginationResponseDTO getRequestListPages(
      @RequestParam(required = false) String requestNo,
      @RequestParam(required = false) Long facilitySeqno,
      @RequestParam(required = false) String requestType,
      @RequestParam(required = false) String title,
      @RequestParam(required = false) String assignedTo,
      @RequestParam(required = false) String category,
      @RequestParam(required = false) LocalDate requestDateFrom,
      @RequestParam(required = false) LocalDate requestDateTo,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) Long size) {
    RequestListSearchDTO requestDTO =
        new RequestListSearchDTO(
            requestNo,
            facilitySeqno,
            requestType,
            title,
            assignedTo,
            category,
            requestDateFrom,
            requestDateTo,
            status);

    PaginationResponseDTO pgResponse = requestService.getRequestListPages(requestDTO, size);

    return pgResponse;
  }

  /**
   * Retrieves the details for the given request header sequence number.
   *
   * @param requestHdrSeqno The notification header sequence number
   * @return The notification details as a NotificationDtlsDTO
   */
  @GetMapping("/viewDetail")
  public ViewRequestDTO getRequestHeader(@PathVariable("requestHdrSeqno") Long requestHdrSeqno) {
    ViewRequestDTO response = requestService.getRequestHeader(requestHdrSeqno);

    return response;
  }

  /**
   * Retrieves the details for the given request header sequence number.
   *
   * @param requestHdrSeqno The notification header sequence number
   * @return The notification details as a NotificationDtlsDTO
   */
  @PostMapping("/SaveRequest")
  public ResponseEntity<RequestHdr> createRequest(@RequestBody SaveRequestDTO dto) {
    RequestHdr response = requestService.saveRequestDetail(dto);

    return ResponseEntity.ok(response);
  }
}
