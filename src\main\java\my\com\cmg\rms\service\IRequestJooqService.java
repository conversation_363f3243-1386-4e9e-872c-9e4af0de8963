package my.com.cmg.rms.service;

import java.util.List;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;

public interface IRequestJooqService {

  List<RequestListDTO> getRequestList(RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO);

  PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size);

  ViewRequestDTO getFullRequestByHdrSeqno(Long requestHdrSeqno);
}
