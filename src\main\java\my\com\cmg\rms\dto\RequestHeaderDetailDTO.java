package my.com.cmg.rms.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

public record RequestHeaderDetailDTO(
    Long requestHdrSeqno,
    String requestNo,
    String requestType,
    String category,
    String subCategory,
    String title,
    String reference,
    String intention,
    String reason,
    Long facilitySeqno,
    String facilityName,
    Long requestedBySeqno,
    String requestedByName,
    LocalDate requestedDate,
    String assignedTo,
    String assignedFrom,
    String status,
    String rejectReason,
    LocalDateTime createdDate,
    LocalDateTime updatedDate,
    String activeFlag,
    Long createdBy,
    Long updatedBy,
    RequestDtlDTO[] requestDetails) {}
