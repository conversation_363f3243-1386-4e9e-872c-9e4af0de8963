package my.com.cmg.rms.service.impl;

import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDTO;
import my.com.cmg.rms.dto.requestDetail.*;
import my.com.cmg.rms.model.RequestDtl;
import my.com.cmg.rms.model.RequestHdr;
import my.com.cmg.rms.model.RequestSupplier;
import my.com.cmg.rms.repository.jpa.RequestHdrRepository;
import my.com.cmg.rms.repository.jpa.RequestRepository;
import my.com.cmg.rms.repository.jpa.SupplierDetailRepository;
import my.com.cmg.rms.service.IRequestService;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestService implements IRequestService {

  private final RequestHdrRepository hdrRepo;
  private final RequestRepository dtlRepo;
  private final SupplierDetailRepository supplierRepo;

  @Override
  public void save(SaveRequestDTO dto) {
    RequestHdr hdr = saveRequestHeader(dto.requestHeader());

    if ("Update".equalsIgnoreCase(dto.requestHeader().requestDataType())
        && dto.requestDtl() != null) {
      saveRequestDetail(dto.requestDtl(), hdr);
    }

    String subCat = dto.requestHeader().subCategory();
    if ("Supplier Master".equalsIgnoreCase(subCat)) {
      saveSupplier(dto.supplierMaster(), hdr);
    } else if ("Item Master".equalsIgnoreCase(subCat)) {
      saveItem(dto.itemMaster(), hdr);
    } else if ("Item Packaging".equalsIgnoreCase(subCat)) {
      savePackaging(dto.itemPackaging(), hdr);
    }
  }

  private RequestHdr saveRequestHeader(RequestHeaderDTO dto) {
    RequestHdr hdr = new RequestHdr();
    hdr.setRequestType(dto.requestType());
    hdr.setCategory(dto.category());
    hdr.setSubCategory(dto.subCategory());
    hdr.setTitle(dto.title());
    hdr.setReason(dto.reason());
    hdr.setReference(dto.reference());
    hdr.setCreatedDate(LocalDateTime.now());
    hdr.setUpdatedDate(LocalDateTime.now());
    hdr.setCreatedBy(1L);
    hdr.setUpdatedBy(1L);
    hdr.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    return hdrRepo.save(hdr);
  }

  private void saveRequestDetail(RequestDtlDTO dto, RequestHdr hdr) {
    RequestDtl dtl = new RequestDtl();
    dtl.setRequestHdr(hdr);
    dtl.setTransSeqno(dto.transSeqno());
    dtl.setTransCode(dto.transCode());
    dtl.setTransName(dto.transName());
    dtl.setTransType(dto.transType());
    dtl.setTransDetails(dto.transDetails());
    dtl.setCreatedDate(LocalDateTime.now());
    dtl.setUpdatedDate(LocalDateTime.now());
    dtl.setCreatedBy(1L);
    dtl.setUpdatedBy(1L);
    dtl.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    dtlRepo.save(dtl);
  }

  private void saveSupplier(SupplierMasterDetailDTO dto, RequestHdr hdr) {
    if (dto == null) return;
    RequestSupplier supplier = new RequestSupplier();
    supplier.setRequestHdr(hdr);
    supplier.setSupplierReqName(dto.supplierReqName());
    supplier.setCompanyRegNo(dto.companyRegNo());
    supplier.setRegExpiryDate(dto.regExpiryDate());
    supplier.setTrsRegNo(dto.trsRegNo());
    supplier.setCompanyStatus(dto.companyStatus());
    supplier.setAddress1(dto.address1());
    supplier.setAddress2(dto.address2());
    supplier.setAddress3(dto.address3());
    supplier.setCity(dto.city());
    supplier.setPostcode(dto.postcode());
    supplier.setState(dto.state());
    supplier.setCountry(dto.country());
    supplier.setMobilePhone(dto.mobilePhone());
    supplier.setEmail(dto.email());
    supplier.setContactPerson(dto.contactPerson());
    supplier.setContactNo(dto.contactNo());

    supplierRepo.save(supplier);
  }

  private void saveItem(ItemMasterDetailDTO dto, RequestHdr hdr) {}

  private void savePackaging(ItemPackagingDetailDTO dto, RequestHdr hdr) {}
}
