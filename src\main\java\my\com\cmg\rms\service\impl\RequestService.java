package my.com.cmg.rms.service.impl;

import jakarta.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.mapper.RequestMapper;
import my.com.cmg.rms.model.RequestDtl;
import my.com.cmg.rms.model.RequestHdr;
import my.com.cmg.rms.repository.jooq.RequestRepositoryJooq;
import my.com.cmg.rms.repository.jpa.RequestDtlRepository;
import my.com.cmg.rms.repository.jpa.RequestHdrRepository;
import my.com.cmg.rms.service.IRequestService;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.PaginationUtil;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestService implements IRequestService {

  private final RequestRepositoryJooq requestRepositoryJooq;
  private final RequestHdrRepository hdrRepo;
  private final RequestDtlRepository dtlRepo;
  private final DSLContext dsl;

  @Override
  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO) {
    PaginationRequestDTO pg = PaginationUtil.pageSorting(pgDTO, new RequestMapper(), true);
    return requestRepositoryJooq.getRequestList(requestDTO, pg);
  }

  @Override
  public PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size) {
    Long pgSize = requestRepositoryJooq.getRequestListCount(requestDTO);
    return PaginationUtil.pagination(size, pgSize);
  }

  @Override
  public ViewRequestDTO getRequestHeader(Long requestHdrSeqno) {
    return requestRepositoryJooq.getRequestHeader(requestHdrSeqno);
  }

  @Override
  public RequestHdr saveRequestHeader(ViewRequestDTO dto) {
    final String methodName = "saveRequestHeader";
    log.info(LogUtil.ENTRY_SERVICE, methodName);

    LocalDateTime now = LocalDateTime.now();

    RequestHdr hdr = new RequestHdr();
    hdr.setRequestNo(generateRequestNo());
    hdr.setRequestType(dto.requestType());
    hdr.setCategory(dto.category());
    hdr.setSubCategory(dto.subCategory());
    hdr.setTitle(dto.title());
    hdr.setReference(dto.reference());
    hdr.setIntention(dto.intention());
    hdr.setReason(dto.reason());
    hdr.setFacilitySeqno(dto.facilitySeqno());
    hdr.setRequestedBySeqno(dto.requestedBySeqno());
    hdr.setRequestedDate(now);
    hdr.setCreatedDate(now);
    hdr.setUpdatedDate(now);
    hdr.setCreatedBy(dto.createdBy() != null ? dto.createdBy() : 1L);
    hdr.setUpdatedBy(dto.updatedBy() != null ? dto.updatedBy() : 1L);
    hdr.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    RequestHdr saved = hdrRepo.save(hdr);
    log.info(LogUtil.EXIT_SERVICE, methodName);
    return saved;
  }

  @Override
  public void saveRequestDetail(RequestDtlDTO dto) {
    final String methodName = "saveRequestDetail";
    log.info(LogUtil.ENTRY_SERVICE, methodName);

    if (dto.requestHdrSeqno() == null) {
      throw new IllegalArgumentException("requestHdrSeqno must not be null");
    }

    RequestHdr hdr =
        hdrRepo
            .findById(dto.requestHdrSeqno())
            .orElseThrow(() -> new EntityNotFoundException("RequestHdr not found"));

    LocalDateTime now = LocalDateTime.now();

    RequestDtl dtl = new RequestDtl();
    dtl.setRequestHdr(hdr);
    dtl.setTransSeqno(dto.transSeqno());
    dtl.setTransCode(dto.transCode());
    dtl.setTransName(dto.transName());
    dtl.setTransType(dto.transType());
    dtl.setTransDetails(dto.transDetails());
    dtl.setCreatedDate(now);
    dtl.setUpdatedDate(now);
    dtl.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);
    dtl.setCreatedBy(dto.createdBy() != null ? dto.createdBy() : 1L);
    dtl.setUpdatedBy(dto.updatedBy() != null ? dto.updatedBy() : 1L);

    dtlRepo.save(dtl);
    log.info(LogUtil.EXIT_SERVICE, methodName);
  }

  private String generateRequestNo() {
    final String methodName = "generateRequestNo";
    log.info(LogUtil.ENTRY_SERVICE, methodName);

    DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yy");
    String year = LocalDateTime.now().format(yearFormatter);
    String codeSeqno = "rm_request_hdr_seq";
    Long sequenceNo = dsl.nextval(codeSeqno.toLowerCase()).longValue();
    String paddedSeq = String.format("%07d", sequenceNo);
    String requestNo = year + paddedSeq + "D";

    log.info("Generated Request No: {}", requestNo);
    log.info(LogUtil.EXIT_SERVICE, methodName);
    return requestNo;
  }

  private void saveNewFacilityMaster() {}

  private void saveNewItemPackaging() {}

  private void saveNewSupplierMaster() {}

  private void saveUpdateFacilityMaster() {}

  private void saveUpdateItemPackaging() {}

  private void saveUpdateSupplierMaster() {}

  public void save() {
    if (dto.requestDataType() == "New") {
      if (dto.requestDataType() == "FM") {
        saveNewFacilityMaster();
      } else if (dto.requestDataType() == "IP") {
        saveNewItemPackaging();
      } else if (dto.requestDataType() == "SM") {
        saveNewSupplierMaster();
      }
    } else if (dto.requestDataType() == "Update") {
      if (dto.requestDataType() == "FM") {
        saveUpdateFacilityMaster();
      } else if (dto.requestDataType() == "IP") {
        saveUpdateItemPackaging();
      } else if (dto.requestDataType() == "SM") {
        saveUpdateSupplierMaster();
      }
    }
  }
}
