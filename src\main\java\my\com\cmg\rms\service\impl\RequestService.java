package my.com.cmg.rms.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDTO;
import my.com.cmg.rms.dto.requestDetail.ItemMasterDetailDTO;
import my.com.cmg.rms.dto.requestDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestDetail.SupplierMasterDetailDTO;
import my.com.cmg.rms.model.RequestHdr;
import my.com.cmg.rms.service.IRequestService;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestService implements IRequestService {

  @Override
  public void save(SaveRequestDTO dto) {
    RequestHdr hdr = saveRequestHeader(dto.requestHeader());
    if ("Update".equalsIgnoreCase(dto.requestHeader().requestType()) && dto.requestDtl() != null) {
      saveRequestDetail(dto.requestDtl(), hdr);
    }
    String subCat = dto.requestHeader().subCategory();
    if ("Supplier Master".equalsIgnoreCase(subCat)) {
      saveSupplier(dto.supplierMaster(), hdr);
    } else if ("Item Master".equalsIgnoreCase(subCat)) {
      saveItem(dto.itemMaster(), hdr);
    } else if ("Item Packaging".equalsIgnoreCase(subCat)) {
      savePackaging(dto.itemPackaging(), hdr);
    }
  }

  private RequestHdr saveRequestHeader(RequestHeaderDTO headerDTO) {
    return null;
  }

  private void saveRequestDetail(RequestDtlDTO dtlDTO, RequestHdr hdr) {}

  private void saveSupplier(SupplierMasterDetailDTO dto, RequestHdr hdr) {}

  private void saveItem(ItemMasterDetailDTO dto, RequestHdr hdr) {}

  private void savePackaging(ItemPackagingDetailDTO dto, RequestHdr hdr) {}
}
