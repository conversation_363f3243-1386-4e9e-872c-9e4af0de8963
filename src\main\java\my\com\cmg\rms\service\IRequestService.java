package my.com.cmg.rms.service;

import java.util.List;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.RequestDtlDTO;
import my.com.cmg.rms.dto.requestDetail.RequestHeaderDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.model.RequestHdr;

public interface IRequestService {

  List<RequestListDTO> getRequestList(RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO);

  PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size);

  ViewRequestDTO getRequestHeader(Long requestHdrSeqno);

  RequestHdr saveRequestHeader(RequestHeaderDTO dto);

  void saveRequestDetail(RequestDtlDTO dto);
}
