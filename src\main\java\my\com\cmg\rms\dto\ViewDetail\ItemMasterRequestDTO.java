package my.com.cmg.rms.dto.viewDetail;

import java.math.BigDecimal;

public record ItemMasterRequestDTO(
    Long itemReqSeqno,
    String itemGroupCode,
    Long genericNameSeqno,
    String otherActiveIngredient,
    String strength,
    Long dosageSeqno,
    String itemName,
    Long itemCatSeqno,
    Long itemSubgroupSeqno,
    Long freqSeqno,
    String administrationRoute,
    String drugIndication,
    String rpItemTypeCode,
    Long itemPackagingSeqno,
    Long skuSeqno,
    Long pkuSeqno,
    BigDecimal conversionFactorNum,
    String packagingDesc,
    String mdcNo,
    Long productSeqno,
    String productName,
    String manufacturedName,
    String importerName,
    String manufacturedAddress,
    String importerAddress,
    String gtinNo,
    String mdaNo) {}
