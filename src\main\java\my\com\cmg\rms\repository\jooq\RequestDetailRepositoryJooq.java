package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.FacilityDetailDTO;
import my.com.cmg.rms.dto.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.ItemsMasterDetailDTO;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record17;
import org.jooq.Record18;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class RequestDetailRepositoryJooq {
  DSLContext dsl;

  public RequestDetailRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public ItemsMasterDetailDTO getItemDetail(Long transSeqno) {

    // condition:
    Condition condition = noCondition();
    condition = condition.and(field("item_req_seqno").eq(transSeqno));

    // fields from ITEMS
    Field<Long> itemReqSeqno = field("item_req_seqno", Long.class).as("itemReqSeqno");
    Field<String> itemGroupCode = field("item_group_code", String.class).as("itemGroupCode");
    Field<Long> genericNameSeqno = field("generic_name_seqno", Long.class).as("genericNameSeqno");
    Field<String> otherActiveIngredient =
        field("other_active_ingredient", String.class).as("otherActiveIngredient");
    Field<String> strength = field("strength", String.class).as("strength");
    Field<Long> dosageSeqno = field("dosage_seqno", Long.class).as("dosageSeqno");
    Field<String> itemName = field("item_name", String.class).as("itemName");
    Field<Long> itemCatSeqno = field("itm_cat_seqno", Long.class).as("itemCatSeqno");
    Field<Long> itemSubgroupSeqno = field("itm_subgroup_seqno", Long.class).as("itemSubgroupSeqno");
    Field<Long> freqSeqno = field("freq_seqno", Long.class).as("freqSeqno");
    Field<String> administrationRoute =
        field("administration_route", String.class).as("administrationRoute");
    Field<String> drugIndication = field("drug_indication", String.class).as("drugIndication");
    Field<String> rpItemTypeCode = field("rp_item_type_code", String.class).as("rpItemTypeCode");
    Field<Long> itemPackagingSeqno =
        field("item_packaging_seqno", Long.class).as("itemPackagingSeqno");
    Field<Long> skuSeqno = field("sku_seqno", Long.class).as("skuSeqno");
    Field<Long> pkuSeqno = field("pku_seqno", Long.class).as("pkuSeqno");
    Field<BigDecimal> conversionFactorNum =
        field("conversion_factor_num", BigDecimal.class).as("conversionFactorNum");
    Field<String> packagingDesc = field("packaging_desc", String.class).as("packagingDesc");
    Field<String> mdcNo = field("mdc_no", String.class).as("mdcNo");
    Field<Long> productSeqno = field("product_seqno", Long.class).as("productSeqno");
    Field<String> productName = field("product_name", String.class).as("productName");
    Field<String> manufacturedName =
        field("manufactured_name", String.class).as("manufacturedName");
    Field<String> importerName = field("importer_name", String.class).as("importerName");
    Field<String> manufacturedAddress =
        field("manufactured_address", String.class).as("manufacturedAddress");
    Field<String> importerAddress = field("importer_address", String.class).as("importerAddress");
    Field<String> gtinNo = field("gtin_no", String.class).as("gtinNo");
    Field<String> mdaNo = field("mda_no", String.class).as("mdaNo");

    var query =
        dsl.select(
                itemReqSeqno,
                itemGroupCode,
                genericNameSeqno,
                otherActiveIngredient,
                strength,
                dosageSeqno,
                itemName,
                itemCatSeqno,
                itemSubgroupSeqno,
                freqSeqno,
                administrationRoute,
                drugIndication,
                rpItemTypeCode,
                itemPackagingSeqno,
                skuSeqno,
                pkuSeqno,
                conversionFactorNum,
                packagingDesc,
                mdcNo,
                productSeqno,
                productName,
                manufacturedName,
                importerName,
                manufacturedAddress,
                importerAddress,
                gtinNo,
                mdaNo)
            .from(TableUtil.table(TableUtil.RM_REQUEST_ITEMS, "ITM"))
            .where(condition)
            .limit(1);

    log.info(LogUtil.QUERY, query);

    ItemsMasterDetailDTO result = query.fetchOneInto(ItemsMasterDetailDTO.class);

    return result;
  }

  public ItemPackagingDetailDTO getItemPackagingDetail(Long transSeqno) {

    // condition
    Condition condition = noCondition();
    condition = condition.and(field("item_packaging_req_seqno").eq(transSeqno));

    // fields from ITEM_PACKAGING
    Field<Long> itemPackagingReqSeqno =
        field("item_packaging_req_seqno", Long.class).as("itemPackagingReqSeqno");
    Field<Long> itemSeqno = field("item_seqno", Long.class).as("itemSeqno");
    Field<String> itemName = field("item_name", String.class).as("itemName");
    Field<String> itemCode = field("item_code", String.class).as("itemCode");
    Field<String> itemPackagingName =
        field("item_packaging_name", String.class).as("itemPackagingName");
    Field<Long> skuSeqno = field("sku_seqno", Long.class).as("skuSeqno");
    Field<Long> pkuSeqno = field("pku_seqno", Long.class).as("pkuSeqno");
    Field<String> conversionFactor =
        field("conversion_factor", String.class).as("conversionFactor");
    Field<String> packagingDesc = field("packaging_desc", String.class).as("packagingDesc");
    Field<String> productList = field("product_list", String.class).as("productList");
    Field<Long> productSeqno = field("product_seqno", Long.class).as("productSeqno");
    Field<String> productName = field("product_name", String.class).as("productName");
    Field<String> manufacturedName =
        field("manufactured_name", String.class).as("manufacturedName");
    Field<String> importerName = field("importer_name", String.class).as("importerName");
    Field<String> manufacturedAddress =
        field("manufactured_address", String.class).as("manufacturedAddress");
    Field<String> importerAddress = field("importer_address", String.class).as("importerAddress");
    Field<String> gtinNo = field("gtin_no", String.class).as("gtinNo");
    Field<String> mdaNo = field("mda_no", String.class).as("mdaNo");

    Select<
            Record18<
                Long,
                Long,
                String,
                String,
                String,
                Long,
                Long,
                String,
                String,
                String,
                Long,
                String,
                String,
                String,
                String,
                String,
                String,
                String>>
        query =
            dsl.select(
                    itemPackagingReqSeqno,
                    itemSeqno,
                    itemName,
                    itemCode,
                    itemPackagingName,
                    skuSeqno,
                    pkuSeqno,
                    conversionFactor,
                    packagingDesc,
                    productList,
                    productSeqno,
                    productName,
                    manufacturedName,
                    importerName,
                    manufacturedAddress,
                    importerAddress,
                    gtinNo,
                    mdaNo)
                .from(TableUtil.table(TableUtil.RM_REQUEST_ITEM_PACKAGING, "ITM"))
                .where(condition)
                .limit(1);

    log.info(LogUtil.QUERY, query);
    ItemPackagingDetailDTO result = query.fetchOneInto(ItemPackagingDetailDTO.class);

    return result;
  }

  public FacilityDetailDTO getFacilityDetail(Long transSeqno) {

    // condition
    Condition condition = noCondition();
    condition = condition.and(field("facility_req_seqno").eq(transSeqno));

    // fields from FACILITY
    Field<Long> facilityReqSeqno = field("facility_req_seqno", Long.class).as("facilityReqSeqno");
    Field<String> facilityReqName = field("facility_req_name", String.class).as("facilityReqName");
    Field<String> facilityReqGroup =
        field("facility_req_group", String.class).as("facilityReqGroup");
    Field<String> ministry = field("ministry", String.class).as("ministry");
    Field<String> facilityReqCategory =
        field("facility_req_category", String.class).as("facilityReqCategory");
    Field<String> facilityReqType = field("facility_req_type", String.class).as("facilityReqType");
    Field<String> address1 = field("address1", String.class).as("address1");
    Field<String> address2 = field("address2", String.class).as("address2");
    Field<String> address3 = field("address3", String.class).as("address3");
    Field<String> city = field("city", String.class).as("city");
    Field<String> postcode = field("postcode", String.class).as("postcode");
    Field<String> state = field("state", String.class).as("state");
    Field<String> country = field("country", String.class).as("country");
    Field<String> mobilePhone = field("mobile_phone", String.class).as("mobilePhone");
    Field<String> email = field("email", String.class).as("email");
    Field<String> contactPerson = field("contact_person", String.class).as("contactPerson");
    Field<String> contactNo = field("contact_no", String.class).as("contactNo");

    Select<
            Record17<
                Long,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String>>
        query =
            dsl.select(
                    facilityReqSeqno,
                    facilityReqName,
                    facilityReqGroup,
                    ministry,
                    facilityReqCategory,
                    facilityReqType,
                    address1,
                    address2,
                    address3,
                    city,
                    postcode,
                    state,
                    country,
                    mobilePhone,
                    email,
                    contactPerson,
                    contactNo)
                .from(TableUtil.table(TableUtil.RM_REQUEST_FACILITY, "FAC"))
                .where(condition)
                .limit(1);

    log.info(LogUtil.QUERY, query);
    FacilityDetailDTO result = query.fetchOneInto(FacilityDetailDTO.class);

    return result;
  }
}
