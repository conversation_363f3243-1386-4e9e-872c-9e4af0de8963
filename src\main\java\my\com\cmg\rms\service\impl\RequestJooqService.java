package my.com.cmg.rms.service.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.mapper.RequestMapper;
import my.com.cmg.rms.repository.jooq.RequestRepositoryJooq;
import my.com.cmg.rms.service.IRequestJooqService;
import my.com.cmg.rms.utils.PaginationUtil;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestJooqService implements IRequestJooqService {

  private final RequestRepositoryJooq requestRepositoryJooq;

  @Override
  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO) {
    PaginationRequestDTO pg = PaginationUtil.pageSorting(pgDTO, new RequestMapper(), true);
    return requestRepositoryJooq.getRequestList(requestDTO, pg);
  }

  @Override
  public PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size) {
    Long pgSize = requestRepositoryJooq.getRequestListCount(requestDTO);
    return PaginationUtil.pagination(size, pgSize);
  }

  @Override
  public ViewRequestDTO getFullRequestByHdrSeqno(Long requestHdrSeqno) {
    return requestRepositoryJooq.getFullRequestByHdrSeqno(requestHdrSeqno);
  }
}
