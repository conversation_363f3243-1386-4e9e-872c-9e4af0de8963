package my.com.cmg.rms.service.impl;

import my.com.cmg.rms.dto.viewDetail.SupplierMasterRequestDTO;
import my.com.cmg.rms.service.IRequestJooqService;
import org.springframework.stereotype.Service;

@Service
public class RequestJooqService implements IRequestJooqService {

  private final SupplierDetailRepository requestSupplierRepository;
  private final SupplierDetailRepositoryJooq supplierDetailRepositoryJooq;

  public RequestJooqService(
      final SupplierDetailRepository requestSupplierRepository,
      final SupplierDetailRepositoryJooq supplierDetailRepositoryJooq) {
    this.requestSupplierRepository = requestSupplierRepository;
    this.supplierDetailRepositoryJooq = supplierDetailRepositoryJooq;
  }

  @Override
  public SupplierMasterRequestDTO getSupplierDetail(Long supplierReqSeqno) {
    return supplierDetailRepositoryJooq.getSupplierDetail(supplierReqSeqno);
  }
}
