package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDetailDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.FacilityMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemPackagingRequestDTO;
import my.com.cmg.rms.dto.viewDetail.SupplierMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.utils.JooqUtil;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class RequestRepositoryJooq {
  DSLContext dsl;

  public RequestRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pageRq) {

    // condition
    Condition condition = noCondition();
    // TODO: search by division

    condition =
        JooqUtil.andCondition(
            condition, field("request_no"), Field::containsIgnoreCase, requestDTO.requestNo());
    condition =
        JooqUtil.andCondition(
            condition, field("facility_seqno"), Field::eq, requestDTO.facilitySeqno());
    condition =
        JooqUtil.andCondition(
            condition, field("request_type"), Field::eq, requestDTO.requestType());
    condition =
        JooqUtil.andCondition(
            condition, field("title"), Field::containsIgnoreCase, requestDTO.title());
    condition =
        JooqUtil.andCondition(condition, field("assigned_to"), Field::eq, requestDTO.assignedTo());
    condition =
        JooqUtil.andCondition(condition, field("category"), Field::eq, requestDTO.category());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::ge, requestDTO.requestDateFrom());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::le, requestDTO.requestDateTo());
    condition = JooqUtil.andCondition(condition, field("status"), Field::eq, requestDTO.status());

    // fields
    Field<Long> requestHdrSeqno = field("request_hdr_seqno", Long.class).as("requestHdrSeqno");
    Field<String> requestNo = field("request_no", String.class).as("requestNo");
    Field<Long> facilitySeqno = field("facility_seqno", Long.class).as("facilitySeqno");
    Field<String> requestType = field("request_type", String.class).as("requestType");
    Field<String> title = field("title", String.class).as("title");
    Field<String> assignedTo = field("assigned_to", String.class).as("assignedTo");
    Field<String> category = field("category", String.class).as("category");
    Field<LocalDate> requestedDate = field("requested_date", LocalDate.class).as("requestDate");
    Field<String> status = field("status", String.class).as("status");

    Select<Record9<Long, String, Long, String, String, String, String, LocalDate, String>> query =
        dsl.select(
                requestHdrSeqno,
                requestNo,
                facilitySeqno,
                requestType,
                title,
                assignedTo,
                category,
                requestedDate,
                status)
            .from(TableUtil.table(TableUtil.RM_REQUEST_HDR, "HDR"))
            .where(condition)
            .orderBy(CoreUtilsRepositoryJooq.getOrderByField(pageRq.sort(), pageRq.sortDirection()))
            .offset((pageRq.page() - 1) * pageRq.size())
            .limit(pageRq.size());

    log.info(LogUtil.QUERY, query);
    List<RequestListDTO> result = query.fetchInto(RequestListDTO.class);

    return result;
  }

  public Long getRequestListCount(RequestListSearchDTO requestDTO) {

    // condition
    Condition condition = noCondition();
    // TODO: search by division

    condition =
        JooqUtil.andCondition(
            condition, field("request_no"), Field::containsIgnoreCase, requestDTO.requestNo());
    condition =
        JooqUtil.andCondition(
            condition, field("facility_seqno"), Field::eq, requestDTO.facilitySeqno());
    condition =
        JooqUtil.andCondition(
            condition, field("request_type"), Field::eq, requestDTO.requestType());
    condition =
        JooqUtil.andCondition(
            condition, field("title"), Field::containsIgnoreCase, requestDTO.title());
    condition =
        JooqUtil.andCondition(condition, field("assigned_to"), Field::eq, requestDTO.assignedTo());
    condition =
        JooqUtil.andCondition(condition, field("category"), Field::eq, requestDTO.category());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::ge, requestDTO.requestDateFrom());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::le, requestDTO.requestDateTo());
    condition = JooqUtil.andCondition(condition, field("status"), Field::eq, requestDTO.status());

    Select<Record1<Integer>> query =
        dsl.selectCount().from(TableUtil.table(TableUtil.RM_REQUEST_HDR)).where(condition);

    log.info(LogUtil.QUERY, query);
    Long result = query.fetchOneInto(Long.class);

    return result;
  }

   @Override
  public ViewRequestDTO getFullRequestByHdrSeqno(Long requestHdrSeqno) {
    RequestHeaderDetailDTO header = repo.getRequestHeader(requestHdrSeqno);
    if (header == null) return null;

    RequestDtlDTO[] details = header.requestDetails();
    if (details == null || details.length == 0) return new ViewRequestDTO(header, null, null, null, null, null);

    Long transSeqno = details[0].transSeqno();
    String transType = details[0].transType();

    // Logic: get only the relevant detail table based on transType
    if ("Item Master".equalsIgnoreCase(transType)) {
      ItemMasterRequestDTO item = repo.getItemDetail(transSeqno);
      return new ViewRequestDTO(header, details, item, null, null, null);
    } else if ("Item Packaging".equalsIgnoreCase(transType)) {
      ItemPackagingRequestDTO itemPackaging = repo.getItemPackagingDetail(transSeqno);
      return new ViewRequestDTO(header, details, null, itemPackaging, null, null);
    } else if ("Facility Master".equalsIgnoreCase(transType)) {
      FacilityMasterRequestDTO facility = repo.getFacilityDetail(transSeqno);
      return new ViewRequestDTO(header, details, null, null, facility, null);
    } else if ("Supplier Master".equalsIgnoreCase(transType)) {
      SupplierMasterRequestDTO supplier = repo.getSupplierDetail(transSeqno);
      return new ViewRequestDTO(header, details, null, null, null, supplier);
    }

    // default return if no match
    return new ViewRequestDTO(header, details, null, null, null, null);
  }
}

}
