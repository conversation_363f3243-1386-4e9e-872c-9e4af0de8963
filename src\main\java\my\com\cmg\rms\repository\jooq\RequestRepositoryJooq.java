package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDTO;
import my.com.cmg.rms.dto.RequestListDTO;
import my.com.cmg.rms.dto.RequestListSearchDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.utils.JooqUtil;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class RequestRepositoryJooq {
  DSLContext dsl;

  public RequestRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pageRq) {

    // condition
    Condition condition = noCondition();
    // TODO: search by division

    condition =
        JooqUtil.andCondition(
            condition, field("request_no"), Field::containsIgnoreCase, requestDTO.requestNo());
    condition =
        JooqUtil.andCondition(
            condition, field("facility_seqno"), Field::eq, requestDTO.facilitySeqno());
    condition =
        JooqUtil.andCondition(
            condition, field("request_type"), Field::eq, requestDTO.requestType());
    condition =
        JooqUtil.andCondition(
            condition, field("title"), Field::containsIgnoreCase, requestDTO.title());
    condition =
        JooqUtil.andCondition(condition, field("assigned_to"), Field::eq, requestDTO.assignedTo());
    condition =
        JooqUtil.andCondition(condition, field("category"), Field::eq, requestDTO.category());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::ge, requestDTO.requestDateFrom());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::le, requestDTO.requestDateTo());
    condition = JooqUtil.andCondition(condition, field("status"), Field::eq, requestDTO.status());

    // fields
    Field<Long> requestHdrSeqno = field("request_hdr_seqno", Long.class).as("requestHdrSeqno");
    Field<String> requestNo = field("request_no", String.class).as("requestNo");
    Field<Long> facilitySeqno = field("facility_seqno", Long.class).as("facilitySeqno");
    Field<String> requestType = field("request_type", String.class).as("requestType");
    Field<String> title = field("title", String.class).as("title");
    Field<String> assignedTo = field("assigned_to", String.class).as("assignedTo");
    Field<String> category = field("category", String.class).as("category");
    Field<LocalDate> requestedDate = field("requested_date", LocalDate.class).as("requestDate");
    Field<String> status = field("status", String.class).as("status");

    Select<Record9<Long, String, Long, String, String, String, String, LocalDate, String>> query =
        dsl.select(
                requestHdrSeqno,
                requestNo,
                facilitySeqno,
                requestType,
                title,
                assignedTo,
                category,
                requestedDate,
                status)
            .from(TableUtil.table(TableUtil.RM_REQUEST_HDR, "HDR"))
            .where(condition)
            .orderBy(CoreUtilsRepositoryJooq.getOrderByField(pageRq.sort(), pageRq.sortDirection()))
            .offset((pageRq.page() - 1) * pageRq.size())
            .limit(pageRq.size());

    log.info(LogUtil.QUERY, query);
    List<RequestListDTO> result = query.fetchInto(RequestListDTO.class);

    return result;
  }

  public Long getRequestListCount(RequestListSearchDTO requestDTO) {

    // condition
    Condition condition = noCondition();
    // TODO: search by division

    condition =
        JooqUtil.andCondition(
            condition, field("request_no"), Field::containsIgnoreCase, requestDTO.requestNo());
    condition =
        JooqUtil.andCondition(
            condition, field("facility_seqno"), Field::eq, requestDTO.facilitySeqno());
    condition =
        JooqUtil.andCondition(
            condition, field("request_type"), Field::eq, requestDTO.requestType());
    condition =
        JooqUtil.andCondition(
            condition, field("title"), Field::containsIgnoreCase, requestDTO.title());
    condition =
        JooqUtil.andCondition(condition, field("assigned_to"), Field::eq, requestDTO.assignedTo());
    condition =
        JooqUtil.andCondition(condition, field("category"), Field::eq, requestDTO.category());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::ge, requestDTO.requestDateFrom());
    condition =
        JooqUtil.andCondition(
            condition, field("requested_date"), Field::le, requestDTO.requestDateTo());
    condition = JooqUtil.andCondition(condition, field("status"), Field::eq, requestDTO.status());

    Select<Record1<Integer>> query =
        dsl.selectCount().from(TableUtil.table(TableUtil.RM_REQUEST_HDR)).where(condition);

    log.info(LogUtil.QUERY, query);
    Long result = query.fetchOneInto(Long.class);

    return result;
  }

  public RequestHeaderDTO getRequestHeader(Long requestHdrSeqnoReq) {
    Condition condition = field("hdr.request_hdr_seqno").eq(requestHdrSeqnoReq);
    log.info(LogUtil.QUERY, condition);

    var result =
        dsl.select(
                field("hdr.request_hdr_seqno", Long.class).as("requestHdrSeqno"),
                field("hdr.request_no", String.class).as("requestNo"),
                field("hdr.request_type", String.class).as("requestType"),
                field("hdr.category", String.class).as("category"),
                field("hdr.sub_category", String.class).as("subCategory"),
                field("hdr.title", String.class).as("title"),
                field("hdr.reference", String.class).as("reference"),
                field("hdr.intention", String.class).as("intention"),
                field("hdr.reason", String.class).as("reason"),
                field("hdr.facility_seqno", Long.class).as("facilitySeqno"),
                field("hdr.facility_name", String.class).as("facilityName"),
                field("hdr.requested_by_seqno", Long.class).as("requestedBySeqno"),
                field("hdr.requested_by_name", String.class).as("requestedByName"),
                field("hdr.requested_date", LocalDate.class).as("requestedDate"),
                field("hdr.assigned_to", String.class).as("assignedTo"),
                field("hdr.assigned_from", String.class).as("assignedFrom"),
                field("hdr.status", String.class).as("status"),
                field("hdr.reject_reason", String.class).as("rejectReason"),
                field("hdr.created_date", LocalDateTime.class).as("createdDate"),
                field("hdr.updated_date", LocalDateTime.class).as("updatedDate"),
                field("hdr.active_flag", String.class).as("activeFlag"),
                field("hdr.created_by", Long.class).as("createdBy"),
                field("hdr.updated_by", Long.class).as("updatedBy"),
                // detail fields
                field("dtl.request_hdr_seqno", Long.class).as("requestHdrSeqno"),
                field("dtl.trans_seqno", Long.class).as("transSeqno"),
                field("dtl.trans_code", String.class).as("transCode"),
                field("dtl.trans_name", String.class).as("transName"),
                field("dtl.trans_type", String.class).as("transType"),
                field("dtl.trans_details", String.class).as("transDetails"),
                field("dtl.created_by", Long.class).as("dtlCreatedBy"),
                field("dtl.updated_by", Long.class).as("dtlUpdatedBy"))
            .from(TableUtil.table(TableUtil.RM_REQUEST_HDR, "hdr"))
            .leftJoin(TableUtil.table(TableUtil.RM_REQUEST_DTL, "dtl"))
            .on(field("hdr.request_hdr_seqno").eq(field("dtl.request_hdr_seqno")))
            .where(condition)
            .fetch();

    if (result.isEmpty()) {
      return null;
    }

    var records = result;
    var r = records.get(0);

    RequestDtlDTO[] details =
        records.stream()
            .filter(row -> row.get("requestDtlSeqno") != null)
            .map(
                row ->
                    new RequestDtlDTO(
                        row.get("requestHdrSeqno", Long.class),
                        row.get("transSeqno", Long.class),
                        row.get("transCode", String.class),
                        row.get("transName", String.class),
                        row.get("transType", String.class),
                        row.get("transDetails", String.class),
                        row.get("dtlCreatedBy", Long.class),
                        row.get("dtlUpdatedBy", Long.class)))
            .toArray(RequestDtlDTO[]::new);

    return new RequestHeaderDTO(
        r.get("requestHdrSeqno", Long.class),
        r.get("requestNo", String.class),
        r.get("requestType", String.class),
        r.get("category", String.class),
        r.get("subCategory", String.class),
        r.get("title", String.class),
        r.get("reference", String.class),
        r.get("intention", String.class),
        r.get("reason", String.class),
        r.get("facilitySeqno", Long.class),
        r.get("facilityName", String.class),
        r.get("requestedBySeqno", Long.class),
        r.get("requestedByName", String.class),
        r.get("requestedDate", LocalDate.class),
        r.get("assignedTo", String.class),
        r.get("assignedFrom", String.class),
        r.get("status", String.class),
        r.get("rejectReason", String.class),
        r.get("createdDate", LocalDateTime.class),
        r.get("updatedDate", LocalDateTime.class),
        r.get("activeFlag", String.class),
        r.get("createdBy", Long.class),
        r.get("updatedBy", Long.class),
        details);
  }
}
